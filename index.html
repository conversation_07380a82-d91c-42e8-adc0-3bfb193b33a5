
<!DOCTYPE html>
<html lang="pt-BR">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Mithra ERP - Soluções em Sistemas</title>
  <meta name="description" content="Site corporativo moderno e responsivo para o Mithra ERP, um sistema de gestão empresarial completo.">
  <meta name="keywords" content="Mithra, ERP, gestão empresarial, software, frigorífico, varejo, telecomunicações, energia, indústria">
  <script src="https://cdn.tailwindcss.com"></script>
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700;800&family=Nunito+Sans:wght@400;700;900&display=swap" rel="stylesheet">
  <style>
    body {
      font-family: 'Inter', sans-serif;
      background-color: #f8fafc; /* Tailwind gray-50 */
      color: #1f2937; /* Tailwind gray-800 */
      scroll-behavior: smooth;
    }
    /* Custom Scrollbar */
    body::-webkit-scrollbar {
      width: 10px;
    }
    body::-webkit-scrollbar-track {
      background: #e5e7eb; /* Tailwind gray-200 */
    }
    body::-webkit-scrollbar-thumb {
      background: #004361; /* primary-s */
      border-radius: 5px;
    }
    body::-webkit-scrollbar-thumb:hover {
      background: #1C7EAF; /* secondary-l */
    }
    .glassmorphism {
      background: rgba(255, 255, 255, 0.6); /* Light background for the glass effect */
      backdrop-filter: blur(10px);
      -webkit-backdrop-filter: blur(10px); /* For Safari */
    }
    .hero-bg-pattern {
      background-image: url("data:image/svg+xml,%3Csvg width='52' height='26' viewBox='0 0 52 26' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill-rule='evenodd'%3E%3Cg fill='%231c7eaf' fill-opacity='0.1'%3E%3Cpath d='M10 10c0-2.21-1.79-4-4-4-3.314 0-6-2.686-6-6h2c0 2.21 1.79 4 4 4 3.314 0 6 2.686 6 6 0 2.21 1.79 4 4 4 3.314 0 6 2.686 6 6 0 2.21 1.79 4 4 4v2c-3.314 0-6-2.686-6-6 0-2.21-1.79-4-4-4-3.314 0-6-2.686-6-6zm25.464-1.95l8.486 8.486-1.414 1.414-8.486-8.486 1.414-1.414z' /%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
    }
  </style>
  <script>
    tailwind.config = {
      theme: {
        extend: {
          colors: {
            'primary-s': '#004361',
            'secondary-l': '#1C7EAF',
            'highlight-l': '#81BCCB',
            'brand-white': '#FFFFFF',
            'brand-dark': '#0F172A', // slate-900
            'brand-light-gray': '#F1F5F9', // slate-100
            'brand-medium-gray': '#64748B', // slate-500
          },
          fontFamily: {
            sans: ['Inter', 'sans-serif'],
            display: ['Nunito Sans', 'sans-serif'],
          },
          animation: {
            'fade-in-up': 'fadeInUp 0.7s ease-out forwards',
            'subtle-pulse': 'subtlePulse 2s infinite ease-in-out',
            'fade-in': 'fadeIn 0.5s ease-out forwards', // Added for AnimatedSection
          },
          keyframes: {
            fadeInUp: {
              '0%': { opacity: '0', transform: 'translateY(20px)' },
              '100%': { opacity: '1', transform: 'translateY(0)' },
            },
            subtlePulse: {
              '0%, 100%': { transform: 'scale(1)' },
              '50%': { transform: 'scale(1.03)' },
            },
            fadeIn: { // Added for AnimatedSection
              '0%': { opacity: '0' },
              '100%': { opacity: '1' },
            }
          }
        },
      },
      plugins: [],
    }
  </script>
</head>
<body>
  <div id="root"></div>
  <script type="importmap">
{
  "imports": {
    "react": "https://esm.sh/react@18.2.0",
    "react-dom/client": "https://esm.sh/react-dom@18.2.0/client",
    "react-router-dom": "https://esm.sh/react-router-dom@6.22.3",
    "react-dom/": "https://esm.sh/react-dom@^19.1.0/",
    "react/": "https://esm.sh/react@^19.1.0/"
  }
}
</script>
  <script type="module" src="/index.tsx"></script>
</body>
</html><link rel="stylesheet" href="index.css">
<script src="index.tsx" type="module"></script>
