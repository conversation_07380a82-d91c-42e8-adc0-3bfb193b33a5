
import React, { useState } from 'react';
import { TRAIL_STEPS_DATA } from '../constants';
import { TrailStep } from '../types';
import { AnimatedSection } from './UI';

interface TrailStepCardProps {
  step: TrailStep;
  isLast: boolean;
}

const TrailStepCard: React.FC<TrailStepCardProps> = ({ step, isLast }) => {
  const [isHovered, setIsHovered] = useState(false);

  return (
    <div className="relative flex flex-col items-center w-1/6 px-2">
      {/* Icon and Title */}
      <div 
        className="relative z-10 flex flex-col items-center cursor-default"
        onMouseEnter={() => setIsHovered(true)}
        onMouseLeave={() => setIsHovered(false)}
      >
        <div className="w-16 h-16 md:w-20 md:h-20 bg-secondary-l rounded-full flex items-center justify-center text-brand-white text-2xl md:text-3xl shadow-lg border-4 border-brand-white transition-all duration-300 transform group-hover:scale-110">
          <i className={step.icon}></i>
        </div>
        <h4 className="mt-3 text-center text-sm md:text-base font-semibold text-primary-s">{step.title}</h4>
      </div>

      {/* Connecting Line (not for the last item) */}
      {!isLast && (
        <div className="absolute top-8 md:top-10 left-1/2 w-full h-1 bg-highlight-l/50 transform -translate-x-0 md:-translate-x-0 z-0"></div>
      )}
      
      {/* Hover Details Box - Tailwind only positioning */}
       {isHovered && (
        <div 
            className="absolute z-20 mt-2 p-4 bg-brand-white rounded-lg shadow-xl w-56 md:w-64 border border-highlight-l/50"
            style={{ top: '100%', left: '50%', transform: 'translateX(-50%)', minHeight: '180px' }} // Position below the icon
            onMouseEnter={() => setIsHovered(true)} // Keep open if mouse moves to box
            onMouseLeave={() => setIsHovered(false)}
        >
            <img src={step.details.image} alt={step.title} className="w-full h-24 md:h-32 object-cover rounded-md mb-2" />
            <p className="text-xs md:text-sm text-brand-medium-gray">{step.details.description}</p>
        </div>
      )}
    </div>
  );
};


export const ProcessingTrail: React.FC = () => {
  return (
    <AnimatedSection className="py-16 bg-gradient-to-b from-highlight-l/10 to-brand-light-gray/10">
      <div className="container mx-auto px-4">
        <h2 className="text-3xl md:text-4xl font-bold font-display text-primary-s text-center mb-4">
          Trilha de Processamento de Carnes
        </h2>
        <p className="text-brand-medium-gray text-center mb-12 md:mb-16 max-w-2xl mx-auto">
          Acompanhe cada etapa do nosso rigoroso processo, garantindo qualidade e segurança do início ao fim.
        </p>
        <div className="relative flex justify-between items-start">
          {/* Horizontal line background */}
          <div className="absolute top-8 md:top-10 left-0 right-0 w-full h-1 bg-highlight-l/30 z-0" style={{width: `calc(100% - ${100/TRAIL_STEPS_DATA.length}%)`, marginLeft: `${100/(TRAIL_STEPS_DATA.length*2)}%`}}></div>
          
          {TRAIL_STEPS_DATA.map((step, index) => (
            <TrailStepCard 
              key={step.id} 
              step={step}
              isLast={index === TRAIL_STEPS_DATA.length - 1}
            />
          ))}
        </div>
      </div>
    </AnimatedSection>
  );
};
