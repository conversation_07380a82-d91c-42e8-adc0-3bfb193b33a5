
import React, { useEffect, useRef, useState } from 'react';
import { Link } from 'react-router-dom';
import { Segment, SegmentFeature, BreadcrumbItem as BreadcrumbItemType } from '../types';

interface AnimatedSectionProps {
  children: React.ReactNode;
  className?: string;
  animationType?: 'fade-in-up' | 'fade-in'; // Add more if needed
  threshold?: number;
  delay?: string; // e.g., 'delay-100', 'delay-200'
}

export const AnimatedSection: React.FC<AnimatedSectionProps> = ({ 
  children, 
  className = '', 
  animationType = 'fade-in-up', 
  threshold = 0.1,
  delay = ''
}) => {
  const [isVisible, setIsVisible] = useState(false);
  const sectionRef = useRef<HTMLDivElement | null>(null);

  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            setIsVisible(true);
            observer.unobserve(entry.target);
          }
        });
      },
      { threshold }
    );

    if (sectionRef.current) {
      observer.observe(sectionRef.current);
    }

    return () => {
      if (sectionRef.current) {
        observer.unobserve(sectionRef.current);
      }
    };
  }, [threshold]);

  const animationClass = animationType === 'fade-in-up' ? 'animate-fade-in-up' : 'animate-fade-in';
  
  return (
    <div
      ref={sectionRef}
      className={`${className} ${isVisible ? `${animationClass} ${delay} opacity-100` : 'opacity-0'}`}
    >
      {children}
    </div>
  );
};


interface SegmentCardProps {
  segment: Segment;
}

export const SegmentCard: React.FC<SegmentCardProps> = ({ segment }) => {
  return (
    <AnimatedSection className="h-full">
      <Link to={`/segmentos/${segment.slug}`} className="block bg-brand-white p-6 rounded-xl shadow-lg hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-1 h-full flex flex-col border border-transparent hover:border-secondary-l">
        <div className="flex flex-col items-center text-center">
          <div className="mb-4 text-secondary-l text-5xl">
            <i className={segment.icon}></i>
          </div>
          <h3 className="text-xl font-bold font-display text-primary-s mb-2">{segment.name}</h3>
          <p className="text-sm text-brand-medium-gray flex-grow">{segment.shortDescription}</p>
        </div>
        <div className="mt-auto pt-4 text-center">
           <span className="text-secondary-l font-semibold hover:underline">
            Saiba Mais <i className="fas fa-arrow-right ml-1 text-xs"></i>
          </span>
        </div>
      </Link>
    </AnimatedSection>
  );
};


interface FeatureListItemProps {
  feature: SegmentFeature;
}

export const FeatureListItem: React.FC<FeatureListItemProps> = ({ feature }) => {
  return (
    <AnimatedSection className="flex items-start space-x-4 p-4 bg-brand-white rounded-lg shadow-sm mb-4" delay="delay-200">
      <div className="text-secondary-l text-3xl mt-1">
        <i className={feature.icon}></i>
      </div>
      <div>
        <h4 className="font-semibold text-primary-s text-lg">{feature.title}</h4>
        <p className="text-brand-medium-gray text-sm">{feature.description}</p>
      </div>
    </AnimatedSection>
  );
};


interface BreadcrumbsProps {
  items: BreadcrumbItemType[];
}

export const Breadcrumbs: React.FC<BreadcrumbsProps> = ({ items }) => {
  return (
    <nav aria-label="Breadcrumb" className="mb-8">
      <ol className="flex items-center space-x-2 text-sm text-brand-medium-gray">
        {items.map((item, index) => (
          <li key={index} className="flex items-center">
            {item.path ? (
              <Link to={item.path} className="hover:text-secondary-l hover:underline">
                {item.label}
              </Link>
            ) : (
              <span className="text-primary-s font-medium">{item.label}</span>
            )}
            {index < items.length - 1 && (
              <i className="fas fa-chevron-right mx-2 text-xs text-gray-400"></i>
            )}
          </li>
        ))}
      </ol>
    </nav>
  );
};
