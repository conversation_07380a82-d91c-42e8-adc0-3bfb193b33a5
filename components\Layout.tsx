
import React, { useState, useEffect } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { NAV_LINKS, APP_NAME, SOCIAL_LINKS } from '../constants';
import { NavLinkItem } from '../types';

interface MithraLogoProps {
  variant?: 'color' | 'white';
  className?: string;
}

export const MithraLogo: React.FC<MithraLogoProps> = ({ variant = 'color', className = '' }) => {
  const primaryColor = variant === 'color' ? '#004361' : '#FFFFFF'; // primary-s or white
  const secondaryColor = variant === 'color' ? '#1C7EAF' : '#E2E8F0'; // secondary-l or light grey
  const highlightColor = variant === 'color' ? '#81BCCB' : '#94A3B8'; // highlight-l or medium grey
  const textColor = variant === 'color' ? '#004361' : '#FFFFFF';

  return (
    <Link to="/" className={`flex items-center space-x-3 ${className}`}>
      <svg width="40" height="40" viewBox="0 0 100 100" xmlns="http://www.w3.org/2000/svg">
        <g transform="skewX(-10) translate(5, 0)">
          <rect x="5" y="15" width="25" height="70" rx="5" fill={primaryColor} />
          <rect x="25" y="15" width="25" height="70" rx="5" fill={secondaryColor} />
          <rect x="45" y="15" width="25" height="70" rx="5" fill={highlightColor} />
        </g>
      </svg>
      <div className="flex flex-col">
        <span className={`font-display font-black text-2xl tracking-tight`} style={{ color: textColor }}>
          MITHRA
        </span>
        <span className={`text-xs font-sans -mt-1`} style={{ color: textColor, opacity: 0.9 }}>
          SOLUÇÕES EM SISTEMAS
        </span>
      </div>
    </Link>
  );
};


export const Header: React.FC = () => {
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const [isScrolled, setIsScrolled] = useState(false);
  const navigate = useNavigate();

  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 20);
    };
    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const handleNavLinkClick = (link: NavLinkItem) => {
    setIsMobileMenuOpen(false);
    if (link.type === 'anchor') {
      // For anchor links, navigate to home if not already there, then scroll
      const targetElement = document.getElementById(link.path.substring(1));
      if (targetElement) {
         // Delay scroll slightly to ensure page is rendered if navigating from another page
        setTimeout(() => {
            targetElement.scrollIntoView({ behavior: 'smooth' });
        }, 100);
      } else {
        navigate('/'); // Navigate to home page first
        setTimeout(() => {
            const el = document.getElementById(link.path.substring(1));
            el?.scrollIntoView({ behavior: 'smooth' });
        }, 300); // Increased delay
      }
    } else {
      navigate(link.path);
    }
  };
  
  const headerClasses = `
    fixed top-0 left-0 right-0 z-50 transition-all duration-300 ease-in-out
    ${isScrolled ? 'py-3 shadow-lg glassmorphism border-b border-gray-200/50' : 'py-4 bg-transparent md:bg-opacity-30 md:bg-primary-s'}
  `;

  const navLinkClasses = `
    block py-2 px-3 rounded md:p-0 transition-colors duration-200
    ${isScrolled ? 'text-primary-s hover:text-secondary-l' : 'text-brand-white hover:text-highlight-l'}
    md:hover:bg-transparent
  `;
   const mobileNavLinkClasses = "block py-2 px-4 text-brand-dark hover:bg-highlight-l/20 rounded";


  return (
    <header className={headerClasses}>
      <div className="container mx-auto px-4 flex justify-between items-center">
        <MithraLogo variant={isScrolled ? 'color' : 'white'} />
        
        {/* Desktop Navigation */}
        <nav className="hidden md:flex space-x-6 items-center">
          {NAV_LINKS.map((link) => (
            <button
              key={link.name}
              onClick={() => handleNavLinkClick(link)}
              className={`${navLinkClasses} font-medium`}
            >
              {link.name}
            </button>
          ))}
           <button 
            onClick={() => handleNavLinkClick({name: "Demo", path: "/#contact", type: "anchor"})}
            className="bg-secondary-l hover:bg-highlight-l text-white font-semibold py-2 px-4 rounded-lg shadow-md transition-all duration-300 transform hover:scale-105"
          >
            Solicitar Demonstração
          </button>
        </nav>

        {/* Mobile Menu Button */}
        <div className="md:hidden">
          <button
            onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
            className={`focus:outline-none ${isScrolled ? 'text-primary-s' : 'text-brand-white'}`}
            aria-label="Abrir menu"
          >
            <i className={`fas ${isMobileMenuOpen ? 'fa-times' : 'fa-bars'} text-2xl`}></i>
          </button>
        </div>
      </div>

      {/* Mobile Menu */}
      {isMobileMenuOpen && (
        <div className="md:hidden absolute top-full left-0 right-0 bg-brand-white shadow-xl rounded-b-lg mx-2 py-2 border border-gray-200">
          <nav className="flex flex-col space-y-1 px-2">
            {NAV_LINKS.map((link) => (
               <button
                key={`mobile-${link.name}`}
                onClick={() => handleNavLinkClick(link)}
                className={mobileNavLinkClasses}
              >
                {link.icon && <i className={`${link.icon} mr-2`}></i>}
                {link.name}
              </button>
            ))}
            <button 
                onClick={() => handleNavLinkClick({name: "Demo", path: "/#contact", type: "anchor"})}
                className="w-full mt-2 bg-secondary-l hover:bg-highlight-l text-white font-semibold py-2.5 px-4 rounded-lg shadow-md transition-all duration-300"
            >
                Solicitar Demonstração
            </button>
          </nav>
        </div>
      )}
    </header>
  );
};

export const Footer: React.FC = () => {
  return (
    <footer className="bg-primary-s text-brand-white py-12">
      <div className="container mx-auto px-4">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-8">
          <div>
            <MithraLogo variant="white" className="mb-4"/>
            <p className="text-sm text-highlight-l/80 leading-relaxed">
              Transformando a gestão de negócios com soluções ERP inovadoras e especializadas para diversos segmentos.
            </p>
          </div>
          <div>
            <h3 className="text-xl font-semibold mb-4 font-display">Links Úteis</h3>
            <ul className="space-y-2">
              {NAV_LINKS.map(link => (
                <li key={`footer-${link.name}`}>
                   <Link to={link.type === 'route' ? link.path : '/'} onClick={() => {
                      if (link.type === 'anchor') {
                        const el = document.getElementById(link.path.substring(1));
                        el?.scrollIntoView({ behavior: 'smooth' });
                      }
                   }} className="hover:text-highlight-l transition-colors duration-200 text-highlight-l/80">
                    {link.name}
                  </Link>
                </li>
              ))}
            </ul>
          </div>
          <div>
            <h3 className="text-xl font-semibold mb-4 font-display">Siga-nos</h3>
            <div className="flex space-x-4 mb-4">
              {SOCIAL_LINKS.map(social => (
                <a key={social.name} href={social.url} target="_blank" rel="noopener noreferrer" aria-label={social.name}
                   className="text-highlight-l/80 hover:text-highlight-l transition-colors duration-200 text-2xl">
                  <i className={social.icon}></i>
                </a>
              ))}
            </div>
            <p className="text-sm text-highlight-l/80">
              Entre em contato: <a href="mailto:<EMAIL>" className="hover:text-highlight-l"><EMAIL></a>
            </p>
          </div>
        </div>
        <div className="border-t border-highlight-l/20 pt-8 text-center text-sm text-highlight-l/60">
          &copy; {new Date().getFullYear()} {APP_NAME}. Todos os direitos reservados.
        </div>
      </div>
    </footer>
  );
};
