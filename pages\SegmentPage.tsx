
import React, { useEffect } from 'react';
import { use<PERSON><PERSON><PERSON>, Link, useNavigate } from 'react-router-dom';
import { SEGMENTS_DATA } from '../constants';
import { Segment } from '../types';
import { AnimatedSection, FeatureListItem, Breadcrumbs } from '../components/UI';
import { ProcessingTrail } from '../components/ProcessingTrail';

const SegmentPage: React.FC = () => {
  const { segmentSlug } = useParams<{ segmentSlug: string }>();
  const navigate = useNavigate();

  const segment = SEGMENTS_DATA.find(s => s.slug === segmentSlug);

  useEffect(() => {
    if (!segment) {
      // Redirect to home or a 404 page if segment not found
      navigate('/');
    }
  }, [segment, navigate]);

  if (!segment) {
    return <div className="container mx-auto px-4 py-12 text-center">Carregando segmento...</div>; // Or a proper loader/404 component
  }

  const breadcrumbItems = [
    { label: 'Início', path: '/' },
    { label: 'Segmentos', path: '/#segments' }, // Link to segment section on home
    { label: segment.name }
  ];
  
  const handleCTAClick = () => {
     const contactSection = document.getElementById('contact');
     if (contactSection) {
       contactSection.scrollIntoView({ behavior: 'smooth' });
     } else {
       navigate('/#contact'); // Fallback if on another page, relies on Header to handle scroll
     }
  };


  return (
    <>
      {/* Hero Section for Segment */}
      <section className="relative py-20 md:py-32 text-brand-white" style={{ backgroundImage: `url(${segment.heroImage})`, backgroundSize: 'cover', backgroundPosition: 'center' }}>
        <div className="absolute inset-0 bg-primary-s opacity-70"></div>
        <AnimatedSection className="container mx-auto px-4 text-center z-10">
          <div className="mb-4 text-6xl text-highlight-l">
            <i className={segment.icon}></i>
          </div>
          <h1 className="text-4xl md:text-5xl font-bold font-display mb-4">{segment.name}</h1>
          <p className="text-xl md:text-2xl max-w-3xl mx-auto text-brand-white/90">{segment.tagline}</p>
        </AnimatedSection>
      </section>

      <div className="container mx-auto px-4 py-12 md:py-16">
        <Breadcrumbs items={breadcrumbItems} />

        <div className="grid md:grid-cols-3 gap-8 md:gap-12">
          {/* Main Content */}
          <main className="md:col-span-2">
            <AnimatedSection>
              <h2 className="text-3xl font-bold font-display text-primary-s mb-6">Visão Geral do Segmento</h2>
              <div className="prose prose-lg max-w-none text-brand-medium-gray leading-relaxed"
                   dangerouslySetInnerHTML={{ __html: segment.fullDescription }} />
            </AnimatedSection>

            <AnimatedSection className="mt-10 md:mt-12" delay="delay-100">
              <h3 className="text-2xl font-bold font-display text-primary-s mb-6">Principais Funcionalidades</h3>
              <div className="space-y-4">
                {segment.features.map((feature, index) => (
                  <FeatureListItem key={index} feature={feature} />
                ))}
              </div>
            </AnimatedSection>
          </main>

          {/* Sidebar */}
          <aside className="md:col-span-1">
            <AnimatedSection className="bg-brand-white p-6 rounded-xl shadow-xl sticky top-24">
              <h3 className="text-xl font-bold font-display text-primary-s mb-4">Fale com um Especialista</h3>
              <p className="text-sm text-brand-medium-gray mb-6">
                Descubra como o Mithra ERP pode ser personalizado para as necessidades exatas do seu negócio.
              </p>
              <button 
                onClick={handleCTAClick}
                className="w-full bg-secondary-l hover:bg-highlight-l text-white font-semibold py-3 px-4 rounded-lg shadow-md transition-all duration-300 transform hover:scale-105"
              >
                Solicitar Demonstração
              </button>
            
              {segment.galleryImages && segment.galleryImages.length > 0 && (
                <div className="mt-8 pt-6 border-t border-brand-light-gray">
                  <h3 className="text-xl font-bold font-display text-primary-s mb-4">Galeria de Imagens</h3>
                  <div className="grid grid-cols-2 gap-3">
                    {segment.galleryImages.map((imgUrl, index) => (
                      <img key={index} src={imgUrl} alt={`${segment.name} - Imagem ${index + 1}`} 
                           className="rounded-lg shadow-md object-cover aspect-video hover:scale-105 transition-transform duration-300" />
                    ))}
                  </div>
                </div>
              )}
            </AnimatedSection>
          </aside>
        </div>
      </div>
      
      {segment.slug === 'frigorifico' && <ProcessingTrail />}
      
      {/* Call to action to see other segments */}
      <section className="py-16 bg-highlight-l/10">
        <div className="container mx-auto px-4 text-center">
          <AnimatedSection>
            <h2 className="text-2xl md:text-3xl font-bold font-display text-primary-s mb-4">
              Explore Outras Soluções Mithra ERP
            </h2>
            <p className="text-brand-medium-gray mb-8 max-w-xl mx-auto">
              Temos a expertise para atender diversos segmentos com a mesma excelência e dedicação.
            </p>
            <Link 
              to="/#segments" 
              className="inline-block bg-primary-s hover:bg-secondary-l text-white font-semibold py-3 px-8 rounded-lg shadow-md transition-all duration-300 transform hover:scale-105"
            >
              Ver Todos os Segmentos
            </Link>
          </AnimatedSection>
        </div>
      </section>
    </>
  );
};

export default SegmentPage;
