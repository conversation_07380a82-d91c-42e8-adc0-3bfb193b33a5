
import React from 'react';
import { HashRouter, Routes, Route, useLocation } from 'react-router-dom';
import HomePage from './pages/HomePage';
import SegmentPage from './pages/SegmentPage';
import { Header, Footer } from './components/Layout'; // Assuming Layout.tsx exports these

const ScrollToTop: React.FC = () => {
  const { pathname } = useLocation();

  React.useEffect(() => {
    window.scrollTo(0, 0);
  }, [pathname]);

  return null;
};

const App: React.FC = () => {
  return (
    <HashRouter>
      <ScrollToTop />
      <div className="flex flex-col min-h-screen bg-brand-light-gray text-brand-dark">
        <Header />
        <main className="flex-grow pt-16 md:pt-20"> {/* Adjust pt based on header height */}
          <Routes>
            <Route path="/" element={<HomePage />} />
            <Route path="/segmentos/:segmentSlug" element={<SegmentPage />} />
            {/* Add a 404 Not Found Page if desired */}
            {/* <Route path="*" element={<NotFoundPage />} /> */}
          </Routes>
        </main>
        <Footer />
      </div>
    </HashRouter>
  );
};

export default App;
