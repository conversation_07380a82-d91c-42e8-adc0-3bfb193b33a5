
import React from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { SEGMENTS_DATA, APP_NAME, ABOUT_US_STATS } from '../constants';
import { AnimatedSection, SegmentCard } from '../components/UI';
import { ContactForm } from '../components/ContactForm';


const HeroSection: React.FC = () => {
  const navigate = useNavigate();
  const handleCTAClick = () => {
     const contactSection = document.getElementById('contact');
     if (contactSection) {
       contactSection.scrollIntoView({ behavior: 'smooth' });
     } else {
       navigate('/#contact'); // Fallback if on another page, relies on <PERSON><PERSON> to handle scroll
     }
  };

  return (
    <section className="relative bg-gradient-to-br from-primary-s via-secondary-l to-highlight-l text-brand-white min-h-[80vh] md:min-h-[calc(100vh-5rem)] flex items-center justify-center py-20 md:py-32 hero-bg-pattern">
      <div className="absolute inset-0 bg-primary-s opacity-30"></div>
      <AnimatedSection className="container mx-auto px-4 text-center z-10">
        <h1 className="text-4xl sm:text-5xl md:text-6xl font-bold font-display mb-6 leading-tight">
          {APP_NAME}: <span className="text-highlight-l">Transformando</span> a Gestão do Seu Negócio
        </h1>
        <p className="text-lg md:text-xl mb-10 max-w-3xl mx-auto text-brand-white/90">
          Soluções ERP completas e personalizadas para otimizar processos, impulsionar o crescimento e simplificar a complexidade do seu dia a dia.
        </p>
        <button 
          onClick={handleCTAClick}
          className="bg-highlight-l hover:bg-brand-white text-primary-s font-bold py-3 px-8 md:py-4 md:px-10 rounded-lg text-lg shadow-xl transition-all duration-300 transform hover:scale-105 animate-subtle-pulse"
        >
          Solicite uma Demonstração Gratuita
        </button>
      </AnimatedSection>
    </section>
  );
};

const SegmentsOverviewSection: React.FC = () => {
  return (
    <section id="segments" className="py-16 md:py-24 bg-brand-light-gray">
      <div className="container mx-auto px-4">
        <AnimatedSection className="text-center mb-12 md:mb-16">
          <div className="inline-block bg-primary-s text-brand-white px-8 py-4 rounded-t-xl shadow-lg">
            <h2 className="text-3xl md:text-4xl font-bold font-display">
              Segmentos de Negócio
            </h2>
          </div>
          <div className="bg-brand-white p-6 rounded-b-xl rounded-tr-xl shadow-lg -mt-1">
             <p className="text-lg text-brand-medium-gray max-w-3xl mx-auto">
              Nós propomos variadas soluções de gestão empresarial através do software Mithra, para diversos segmentos de negócio. Encontre a solução ideal para sua empresa.
            </p>
          </div>
        </AnimatedSection>
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 2xl:grid-cols-5 gap-6 md:gap-8">
          {SEGMENTS_DATA.map((segment, index) => (
             <SegmentCard key={segment.id} segment={segment} />
          ))}
        </div>
      </div>
    </section>
  );
};

const AboutUsSection: React.FC = () => {
  return (
    <section id="about-us" className="py-16 md:py-24 bg-brand-white">
      <div className="container mx-auto px-4">
        <div className="grid md:grid-cols-2 gap-12 items-center">
          <AnimatedSection>
            <h2 className="text-3xl md:text-4xl font-bold font-display text-primary-s mb-6">
              Sobre o <span className="text-secondary-l">{APP_NAME}</span>
            </h2>
            <p className="text-lg text-brand-medium-gray mb-4 leading-relaxed">
              Com mais de duas décadas de experiência, o Mithra ERP se consolidou como uma referência em sistemas de gestão empresarial, oferecendo tecnologia de ponta e um profundo conhecimento das particularidades de cada segmento.
            </p>
            <p className="text-brand-medium-gray mb-6 leading-relaxed">
              Nossa missão é empoderar empresas através de soluções inteligentes que simplificam processos, otimizam recursos e impulsionam resultados. Conte com um parceiro dedicado ao seu sucesso.
            </p>
            <Link to="/segmentos/industria" /* Example link, adjust as needed */
                  className="inline-block bg-secondary-l hover:bg-highlight-l text-white font-semibold py-3 px-6 rounded-lg shadow-md transition-all duration-300 transform hover:scale-105">
              Conheça Nossas Soluções
            </Link>
          </AnimatedSection>
          <AnimatedSection className="grid grid-cols-2 gap-6" delay="delay-200">
            {ABOUT_US_STATS.map(stat => (
                <div key={stat.label} className="bg-highlight-l/10 p-6 rounded-xl text-center shadow-lg">
                    <div className="text-4xl font-bold text-secondary-l mb-2 font-display">{stat.value}</div>
                    <div className="text-sm text-primary-s font-medium">{stat.label}</div>
                </div>
            ))}
          </AnimatedSection>
        </div>
      </div>
    </section>
  );
};

const ContactSection: React.FC = () => {
  return (
    <section id="contact" className="py-16 md:py-24 bg-gradient-to-b from-highlight-l/10 to-secondary-l/10">
      <div className="container mx-auto px-4">
        <ContactForm />
      </div>
    </section>
  );
};


const HomePage: React.FC = () => {
  return (
    <>
      <HeroSection />
      <SegmentsOverviewSection />
      <AboutUsSection />
      <ContactSection />
    </>
  );
};

export default HomePage;
