
export interface NavLinkItem {
  name: string;
  path: string;
  type: 'route' | 'anchor';
  icon?: string; // Optional: Font Awesome class
}

export interface SegmentFeature {
  icon: string; // Font Awesome class
  title: string;
  description: string;
}

export interface Segment {
  id: string;
  slug: string;
  name: string;
  tagline: string;
  icon: string; // Font Awesome class for card/hero
  heroImage: string; // URL for hero background
  shortDescription: string; // For SegmentCard
  fullDescription: string; // HTML content for SegmentPage
  features: SegmentFeature[];
  galleryImages?: string[]; // URLs for image gallery
}

export interface BreadcrumbItem {
  label: string;
  path?: string;
}

export interface TrailStep {
  id: number;
  icon: string; // Font Awesome class
  title: string;
  details: {
    description: string;
    image: string; // URL for GIF/image
  };
}

export interface SocialLink {
  name: string;
  icon: string; // Font Awesome class
  url: string;
}
