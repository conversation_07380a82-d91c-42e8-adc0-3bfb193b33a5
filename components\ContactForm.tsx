
import React, { useState, FormEvent } from 'react';
import { AnimatedSection } from './UI';


export const ContactForm: React.FC = () => {
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    company: '',
    message: '',
  });
  const [status, setStatus] = useState<'idle' | 'loading' | 'success' | 'error'>('idle');
  const [errors, setErrors] = useState<Partial<typeof formData>>({});

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    setFormData({ ...formData, [e.target.name]: e.target.value });
    if(errors[e.target.name as keyof typeof errors]) {
      setErrors({ ...errors, [e.target.name]: undefined });
    }
  };

  const validateForm = (): boolean => {
    const newErrors: Partial<typeof formData> = {};
    if (!formData.name.trim()) newErrors.name = 'Nome é obrigatório.';
    if (!formData.email.trim()) {
      newErrors.email = 'E-mail é obrigatório.';
    } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
      newErrors.email = 'Formato de e-mail inválido.';
    }
    if (!formData.message.trim()) newErrors.message = 'Mensagem é obrigatória.';
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: FormEvent) => {
    e.preventDefault();
    if (!validateForm()) return;

    setStatus('loading');
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1500));
    
    // Simulate random success/error for demo
    if (Math.random() > 0.1) { // 90% success rate
        setStatus('success');
        setFormData({ name: '', email: '', company: '', message: '' });
    } else {
        setStatus('error');
    }
  };

  return (
    <AnimatedSection className="bg-brand-white p-8 md:p-12 rounded-xl shadow-2xl w-full max-w-2xl mx-auto">
      <h2 className="text-3xl font-bold font-display text-primary-s text-center mb-2">
        Fale Conosco
      </h2>
      <p className="text-brand-medium-gray text-center mb-8">
        Pronto para transformar sua gestão? Preencha o formulário abaixo e solicite uma demonstração personalizada.
      </p>
      
      <form onSubmit={handleSubmit} className="space-y-6">
        <div>
          <label htmlFor="name" className="block text-sm font-medium text-primary-s mb-1">Nome Completo</label>
          <input type="text" name="name" id="name" value={formData.name} onChange={handleChange}
                 className={`w-full px-4 py-2.5 border ${errors.name ? 'border-red-500' : 'border-gray-300'} rounded-lg focus:ring-2 focus:ring-secondary-l focus:border-secondary-l transition-shadow`} />
          {errors.name && <p className="text-red-500 text-xs mt-1">{errors.name}</p>}
        </div>
        <div>
          <label htmlFor="email" className="block text-sm font-medium text-primary-s mb-1">E-mail</label>
          <input type="email" name="email" id="email" value={formData.email} onChange={handleChange}
                 className={`w-full px-4 py-2.5 border ${errors.email ? 'border-red-500' : 'border-gray-300'} rounded-lg focus:ring-2 focus:ring-secondary-l focus:border-secondary-l transition-shadow`} />
          {errors.email && <p className="text-red-500 text-xs mt-1">{errors.email}</p>}
        </div>
        <div>
          <label htmlFor="company" className="block text-sm font-medium text-primary-s mb-1">Empresa (Opcional)</label>
          <input type="text" name="company" id="company" value={formData.company} onChange={handleChange}
                 className="w-full px-4 py-2.5 border border-gray-300 rounded-lg focus:ring-2 focus:ring-secondary-l focus:border-secondary-l transition-shadow" />
        </div>
        <div>
          <label htmlFor="message" className="block text-sm font-medium text-primary-s mb-1">Mensagem</label>
          <textarea name="message" id="message" rows={4} value={formData.message} onChange={handleChange}
                    className={`w-full px-4 py-2.5 border ${errors.message ? 'border-red-500' : 'border-gray-300'} rounded-lg focus:ring-2 focus:ring-secondary-l focus:border-secondary-l transition-shadow`}></textarea>
          {errors.message && <p className="text-red-500 text-xs mt-1">{errors.message}</p>}
        </div>
        
        <button type="submit" disabled={status === 'loading'}
                className="w-full bg-secondary-l hover:bg-highlight-l text-white font-semibold py-3 px-6 rounded-lg shadow-md transition-all duration-300 transform hover:scale-105 focus:outline-none focus:ring-2 focus:ring-secondary-l focus:ring-opacity-50 disabled:opacity-70 disabled:cursor-not-allowed">
          {status === 'loading' ? (
            <div className="flex items-center justify-center">
              <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              Enviando...
            </div>
          ) : 'Solicitar Demonstração'}
        </button>
        
        {status === 'success' && (
          <AnimatedSection animationType="fade-in">
            <div className="mt-4 p-3 bg-green-100 border border-green-400 text-green-700 rounded-lg text-center">
              <i className="fas fa-check-circle mr-2"></i>Sua mensagem foi enviada com sucesso! Entraremos em contato em breve.
            </div>
          </AnimatedSection>
        )}
        {status === 'error' && (
          <AnimatedSection animationType="fade-in">
            <div className="mt-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded-lg text-center">
              <i className="fas fa-exclamation-circle mr-2"></i>Ocorreu um erro ao enviar sua mensagem. Tente novamente mais tarde.
            </div>
          </AnimatedSection>
        )}
      </form>
    </AnimatedSection>
  );
};
